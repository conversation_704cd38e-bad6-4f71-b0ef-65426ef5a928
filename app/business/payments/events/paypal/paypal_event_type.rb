# frozen_string_literal: true

class PaypalEventType
  CUSTOMER_DISPUTE_CREATED = "CUSTOMER.DISPUTE.CREATED"
  CUSTOMER_DISPUTE_RESOLVED = "CUSTOMER.DISPUTE.RESOLVED"
  PAYMENT_CAPTURE_COMPLETED = "PAYMENT.CAPTURE.COMPLETED"
  PAYMENT_CAPTURE_DENIED = "PAYMENT.CAPTURE.DENIED"
  PAYMENT_CAPTURE_REVERSED = "PAYMENT.CAPTURE.REVERSED"
  PAYMENT_CAPTURE_REFUNDED = "PAYMENT.CAPTURE.REFUNDED"

  MERCHANT_ONBOARDING_SELLER_GRANTED_CONSENT = "CUSTOMER.MERCHANT-INTEGRATION.SELLER-CONSENT-GRANTED"
  MERCHANT_ONBOARDING_COMPLETED = "MERCHANT.ONBOARDING.COMPLETED"
  MERCHANT_PARTNER_CONSENT_REVOKED = "MERCHANT.PARTNER-CONSENT.REVOKED"
  MERCHANT_IDENTITY_AUTH_CONSENT_REVOKED = "IDENTITY.AUTHORIZATION-CONSENT.REVOKED"
  MERCHANT_CAPABILITY_UPDATED = "CUSTOMER.MERCHANT-INTEGRATION.CAPABILITY-UPDATED"
  MERCHANT_SUBSCRIPTION_UPDATED = "CUSTOMER.MERCHANT-INTEGRATION.PRODUCT-SUBSCRIPTION-UPDATED"
  MERCHANT_EMAIL_CONFIRMED = "CUSTOMER.MERCHANT-INTEGRATION.SELLER-EMAIL-CONFIRMED"

  ORDER_API_EVENTS = [CUSTOMER_DISPUTE_CREATED, CUSTOMER_DISPUTE_RESOLVED, PAYMENT_CAPTURE_COMPLETED,
                      PAYMENT_CAPTURE_DENIED, PAYMENT_CAPTURE_REVERSED, PAYMENT_CAPTURE_REFUNDED].freeze

  MERCHANT_ACCOUNT_EVENTS = [MERCHANT_ONBOARDING_COMPLETED, MERCHANT_PARTNER_CONSENT_REVOKED,
                             MERCHANT_CAPABILITY_UPDATED, MERCHANT_SUBSCRIPTION_UPDATED,
                             MERCHANT_EMAIL_CONFIRMED, MERCHANT_IDENTITY_AUTH_CONSENT_REVOKED,
                             MERCHANT_ONBOARDING_SELLER_GRANTED_CONSENT].freeze
end
